import { Component, HostListener, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { ToastrService } from 'ngx-toastr';
import { PhysicianService } from 'src/app/services/physician/physician.service';
import { DatePipe } from '@angular/common';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { forkJoin } from 'rxjs';
declare let $: any;

@Component({
  selector: 'app-m-my-group-patients',
  templateUrl: './m-my-group-patients.component.html',
  styleUrls: ['./m-physician.scss']
})
export class MMyGroupPatientsComponent implements OnInit {

  public submitted: boolean = false; 
  public FilterForm: FormGroup; 
  public listOfFacilities: Array<any> = [];
  public request: any = {};
  public request2: any = {};
  public p: number = 1;
  public listOfPatients: Array<any> = [];
  public timeout: any = null;
  public searchByName: string = ''; 
  public totalCount: number = 0;
  public listOfGroups: Array<any> = []; 
  public listOfPhysiciansandcount: Array<any> = []; 
  public selectedListOfPhysiciansandcount: Array<any> = []; 
  public userName: string;
  device = false; 
  public filterObj: any = {};
  hideSideNav = false;
  toggleType = 'close';
  orderBy = 'desc'; 
  public sortColumnBy: string = 'room_Number'; 
  public mDdlPhysicianSettings: IDropdownSettings = {}; 
  sortOrders = ['desc', 'asc'];
  sortOptions = [
    {
      id: 'account_Number', name: 'Account Number', sortOrders: ['desc', 'asc']
    },
    {
      id: 'patient_Name', name: 'Patient Name', sortOrders: ['desc', 'asc']
    },
    {
      id: 'facility_Name', name: 'Facility', sortOrders: ['desc', 'asc']
    },
    {
      id: 'age', name: 'Age', sortOrders: ['desc', 'asc']
    },
    {
      id: 'room_Number', name: 'Room', sortOrders: ['desc', 'asc']
    },
    {
      id: 'admission_Type', name: 'Patient Type', sortOrders: ['desc', 'asc']
    },
    {
      id: 'arithmetic_Mean_LOS', name: 'LOS', sortOrders: ['desc', 'asc']
    },
    {
      id: 'reimbursement_Type', name: 'Payer Class', sortOrders: ['desc', 'asc']
    },
    {
      id: 'attending_Physician_InApp', name: 'Attending Provider', sortOrders: ['desc', 'asc']
    }
  ];

  constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService
    , private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly toastr: ToastrService, private readonly physicianService: PhysicianService, public datepipe: DatePipe) { }

  ngOnInit() {
    this.appComp.loadPageName('My Group Patients', 'physicianTab');
    this.userName = this.appComp.userAccess.firstName + ' ' + this.appComp.userAccess.lastName;
    this.FilterForm = this.fb.group({
      ddlFacility: [''],
      ddlGroups: [''],
      ddlphybygroup: ['']
    });
    this.mDdlPhysicianSettings = {
      singleSelection: false,
      idField: 'phyName',
      textField: 'phyNameWithCount',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 2,
      allowSearchFilter: true,
      enableCheckAll: true,
      closeDropDownOnSelection:true
    };
    this.filterObj = history.state.filterObj;
    if (this.filterObj) {
      this.FilterForm.get('ddlFacility')?.setValue(this.filterObj.ddlFacility);
      this.searchByName = this.filterObj.searchByName;
      this.p = this.filterObj.p;
      this.getFacilities();
      this.FilterForm.get('ddlGroups')?.setValue(this.filterObj.ddlGroups);
      this.selectedListOfPhysiciansandcount=this.filterObj.ddlphybygroup;
      this.getGroupsByFacilityForFilter();
      this.getMyGroupPatientsDetails(this.p);
    }
    else {
      this.getFacilitiesAndPatients();
    }
    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (window.innerWidth <= 575 || window.innerWidth >= 575)) {
      // some code..
      this.device = true;
    } else {
      this.device = false;
    }

    
  }
  get f() { return this.FilterForm.controls; }

  getFacilitiesAndPatients() {
    this.submitted = true;
    this.commonServ.getFacilitiesByUserType('PHYSICIAN').subscribe((p: any) => {
      this.listOfFacilities = p;
      if (this.listOfFacilities.length > 0) {
        this.FilterForm.get('ddlFacility')?.setValue(this.listOfFacilities[0].facilityName);
        this.getGroupsByFacility(false);
      }
    }, error => { console.error(error.status); });
  }

  getFacilities() {
    this.submitted = true;
    this.commonServ.getFacilitiesByUserType('PHYSICIAN').subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }

  getGroupsByFacility(flag) {
    this.submitted = true;
    this.p = 1;
    if(flag){
      this.FilterForm.get('ddlGroups')?.setValue('');
      this.FilterForm.get('ddlphybygroup')?.setValue('');
    }
    this.commonServ.startLoading();
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.Role = this.encrDecr.set('PHYSICIAN');

    this.request2.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request2.GroupName = this.encrDecr.set(this.FilterForm.value.ddlGroups);
    let physicianList = '';
    if (this.FilterForm.value.ddlphybygroup != '') {
      this.FilterForm.value.ddlphybygroup.map(item => {
        physicianList = physicianList + item.phyName + ',';
      });
    }
    this.request2.PhyName = this.encrDecr.set(physicianList);
    this.request2.sPageSize = this.encrDecr.set('14');
    this.request2.sPageIndex = this.encrDecr.set(this.p);
    this.request2.Searchstring = this.encrDecr.set(this.searchByName);
    this.request2.SortColumn = this.encrDecr.set(this.sortColumnBy);
    this.request2.SortExpression = this.encrDecr.set(this.orderBy);
    forkJoin(
      this.commonServ.GetGroupsByFacility(this.request),
      this.physicianService.GetMyGroupPatientsDetails(this.request2)
    ).subscribe((p: any) => {
      this.listOfGroups = p[0];
      this.listOfPatients = p[1].physicianPatientsResponses;
      this.totalCount = p[1].groupPatientCount;
      this.listOfPhysiciansandcount =p[1].physiciansAndPatCounts;
      this.request = {};
      this.request2 = {};
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  getGroupsByFacilityForFilter() {
    this.submitted = true;
    this.commonServ.startLoading();
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.Role = this.encrDecr.set('PHYSICIAN');
    this.commonServ.GetGroupsByFacility(this.request).subscribe((p: any) => {
      this.listOfGroups = p;
      this.request = {};
    }, error => { console.error(error.status); }
    )
  }

  getphyciansBygroup() {
    this.submitted = true;
    this.listOfPhysiciansandcount = [];
    this.commonServ.startLoading();
    this.FilterForm.get('ddlphybygroup')?.setValue('');
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.GroupName = this.encrDecr.set(this.FilterForm.value.ddlGroups);
    this.request.PhyName = this.encrDecr.set('');
    this.request.BillerAccess = this.encrDecr.set('YES');
    this.request.MyGroupPatient = this.encrDecr.set('1');
    this.request.sPageSize = this.encrDecr.set('14');
    this.request.sPageIndex = this.encrDecr.set('1');
    this.request.Searchstring = this.encrDecr.set(this.searchByName);
    this.request.AdmissionFromDate = this.encrDecr.set("");
    this.request.AdmissionToDate = this.encrDecr.set("");
    this.physicianService.GetMyGroupPatientsDetails(this.request).subscribe((p: any) => {
      this.listOfPatients = p.physicianPatientsResponses;
      this.totalCount = p.groupPatientCount;
      this.listOfPhysiciansandcount = p.physiciansAndPatCounts;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); }
    )
  }
  updateSortObj(obj: any) {
    this.sortColumnBy = obj.sortColumnBy;
    this.orderBy = obj.orderBy;
  }
  getMyGroupPatientsDetails(pno) {
    this.p = pno;
    this.submitted = true;
    this.commonServ.startLoading();
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.GroupName = this.encrDecr.set(this.FilterForm.value.ddlGroups);
    let physicianList = '';
    if (this.FilterForm.value.ddlphybygroup != '') {
      this.FilterForm.value.ddlphybygroup.map(item => {
        physicianList = physicianList + item.phyName + ',';
      });
    }
    this.request.PhyName = this.encrDecr.set(physicianList);
    this.request.sPageSize = this.encrDecr.set('14');
    this.request.sPageIndex = this.encrDecr.set(pno);
    this.request.Searchstring = this.encrDecr.set(this.searchByName);
    this.request.SortColumn = this.encrDecr.set(this.sortColumnBy);
    this.request.SortExpression = this.encrDecr.set(this.orderBy);
    this.physicianService.GetMyGroupPatientsDetails(this.request).subscribe((p: any) => {
      this.listOfPatients = p.physicianPatientsResponses;
      this.totalCount = p.groupPatientCount;
      this.request = {};
      this.submitted=false;
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); }
    )
  }
  getphyciansBygroupPhyAll() {
    this.submitted = true;
    this.listOfPhysiciansandcount = [];
    this.commonServ.startLoading();
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.GroupName = this.encrDecr.set(this.FilterForm.value.ddlGroups);
    this.request.PhyName = this.encrDecr.set('');
    this.request.BillerAccess = this.encrDecr.set('YES');
    this.request.MyGroupPatient = this.encrDecr.set('1');
    this.request.sPageSize = this.encrDecr.set('14');
    this.request.sPageIndex = this.encrDecr.set('1');
    this.request.Searchstring = this.encrDecr.set(this.searchByName);
    this.request.AdmissionFromDate = this.encrDecr.set("");
    this.request.AdmissionToDate = this.encrDecr.set("");
    this.physicianService.GetMyGroupPatientsDetails(this.request).subscribe((p: any) => {
      this.listOfPatients = p.physicianPatientsResponses;
      this.totalCount = p.groupPatientCount;
      this.listOfPhysiciansandcount = p.physiciansAndPatCounts;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); }
    )
  }
  RemoveGroupForPatient(item) {
      this.submitted = true;
      this.commonServ.startLoading();
      this.request.Account_Number = this.encrDecr.set(item.account_Number);
      this.request.FacilityName = this.encrDecr.set(item.facility_Name);
      this.physicianService.RemoveGroupForPatient(this.request).subscribe((p: any) => {
        this.request = {};
        this.getMyGroupPatientsDetails(this.p);
        this.toastr.success("Removed groups for Patient successfully", '', { timeOut: 2500 });
      }, error => { console.error(error.status); }
      )
  }

  ddlFacilityChange()
  {
    this.submitted = true;
    this.FilterForm.get('ddlGroups')?.setValue('');
    this.FilterForm.get('ddlphybygroup')?.setValue(''); 
     this.listOfPhysiciansandcount = [];   
     this.getGroupsByFacilityForFilter();
    this.getMyGroupPatientsDetails(1);
  }

  FilterPatientDetails() {
    this.submitted = true;
    this.getMyGroupPatientsDetails(1);
  }

  onKeyPatientSearch() {
    if (this.FilterForm.invalid) {
      return;
    }
    this.getMyGroupPatientsDetails(1);
  }

  toggleSideNav(type): void {
    this.toggleType = type;
    this.hideSideNav = !this.hideSideNav;
  }
  onChangeForSort(event) {
    const deviceValue = event.value;
    this.sortOrders = this.sortOptions.filter(x => x.id == deviceValue)[0].sortOrders;
    this.sortColumnBy=deviceValue;
    this.updateSortObj({ sortColumnBy: this.sortColumnBy, orderBy: this.orderBy });
    this.getMyGroupPatientsDetails(this.p);
  }
  onChangeForSortOrder(event) {
    const deviceValue = event.value;
    this.orderBy = deviceValue;
    this.updateSortObj({ sortColumnBy: this.sortColumnBy, orderBy: this.orderBy });
    this.getMyGroupPatientsDetails(this.p);
  }

  //@HostListener('window:scroll', ['$event'])
  //scrollHandler(event: any) {
  //  if (window.innerHeight + window.scrollY >= document.body.offsetHeight && !this.submitted) {
  //    console.log("bottom of the page");
  //    this.submitted = true;
  //    this.getMyGroupPatientsDetails(this.p+1);
  //  }
  //}

}
