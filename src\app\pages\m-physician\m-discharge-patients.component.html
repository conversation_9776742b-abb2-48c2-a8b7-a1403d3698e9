<div class="patient-list-container">
    <!-- Header Section -->
    <form (ngSubmit)="getDischargePatientData(1)" (keydown.enter)="getDischargePatientData(1)" [formGroup]="FilterForm">
        <div class="header-section">
            <!-- Search Bar -->
            <div class="search-container">
                <div class="search-input-wrapper">
                    <i class="fas fa-search search-icon" (click)="onKeyPatientSearch()"></i>
                    <input type="text" class="search-input" placeholder="Search Name, Account, MRN, Room"
                        [(ngModel)]="searchByName" [ngModelOptions]="{standalone: true}">
                </div>
                <!-- <button class="sort-btn">
                    <img src="assets/icons/icon-sort.svg" class="img-icon">
                </button> -->
                <button class="filter-btn" title="Submit Filter">
                    <img src="assets/icons/icon-filter-active.svg" class="img-icon">
                </button>
            </div>

            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <div class="facility-dropdown">
                    <mat-select id="ddlFacility" class="form-control" formControlName="ddlFacility" (selectionChange)="getDischargePatientData(1)"
                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                        <mat-option value="">---Select Facility---</mat-option>
                        <mat-option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                            <span>{{s.facilityName}}</span>
                        </mat-option>
                    </mat-select>
                </div>
            </div>
            <div class="filter-tabs mt-2">
                <div class="facility-dropdown">
                    <input class="form-control" title="Discharge From Date" (change)="getDischargePatientData(1)"
                        [ngClass]="{ 'is-invalid': submitted && f['txtFromDate'].errors}" type="date"
                        formControlName="txtFromDate" id="txtFromDate" placeholder="mm/dd/yyyy">
                </div>
                <div class="facility-dropdown">
                    <input class="form-control input-border" title="Discharge To Date" (change)="getDischargePatientData(1)"
                        [ngClass]="{ 'is-invalid': submitted && f['txtToDate'].errors}" type="date"
                        formControlName="txtToDate" id="txtToDate" placeholder="mm/dd/yyyy">
                </div>
            </div>

        </div>
    </form>

    <!-- Patient Cards Starts -->
    <app-m-patients [listOfPatients]="listOfPatients" [p]="p" [pageName]="'/m/physician/discharge-patients'"
        [FilterForm]="FilterForm" [totalCount]="totalCount" [searchByName]="searchByName" [device]="device"
        (enventUpdateSortObj)="updateSortObj($event)" [orderBy]="orderBy" [sortColumnBy]="sortColumnBy"
        (eventListOfPatients)="getDischargePatientData($event)"></app-m-patients>
    <!-- Patient Cards Ends -->

</div>