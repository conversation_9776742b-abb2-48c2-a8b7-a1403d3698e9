<div class="w-full">
    <div class="p-3 mb-8">
        <div class="search-container">
            <div class="search-input-wrapper">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Search by keyword" [(ngModel)]="searchModfier"
                    [ngModelOptions]="{standalone: true}">
            </div>
        </div>
        <div class="flex flex-col">
            <div class="flex border-b py-1" *ngFor="let item of listOfModifier |gridFilter:{modifiersname:searchModfier}:false">
                <mat-checkbox [color]="'primary'" [checked]="item.isExist"
                    (change)="chkChangeEvent(item.modifiersname, $event)" id="{{item.modifierS_ID}}"
                    value="{{item.modifiersname}}">
                    <span>{{item.modifiersname}}</span>
                </mat-checkbox>
            </div>
        </div>
    </div>
    <div class="upload-section upload-footer">
        <button class="add-btn" (click)="addModifiers(cptCode)">Save</button>
    </div>
</div>