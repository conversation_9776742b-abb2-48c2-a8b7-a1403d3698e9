import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';

@Component({
  selector: 'app-m-undo-discharge-patient',
  templateUrl: './m-undo-discharge-patient.component.html',
  styleUrls: ['./m-physician.scss']
})
export class MUndoDischargePatientComponent {
  @Input() patient: any = {};
  @Output() eventListOfPatients = new EventEmitter<Array<any>>();
  @Output() eventCloseUndoDischargePopup = new EventEmitter();
  public listOfPatients: Array<any> = [];
  public request: any = {};
  public submitted: boolean = false;
  constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService, private readonly toastr: ToastrService) { }

  confrimUndoDischarge(item) {
    this.commonServ.startLoading();
    this.request.Account_Number = this.encrDecr.set(item.account_Number);
    this.request.Patient_Name = this.encrDecr.set(item.patient_Name);
    this.request.Facility_Name = this.encrDecr.set(item.facility_Name);
    this.commonServ.UnDischargePatient(this.request).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.eventListOfPatients.emit(this.listOfPatients);
      this.toastr.success("Patient Un-Discharged successfully");
    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
  }

  closeUndoDischargePopup(){
    this.eventCloseUndoDischargePopup.emit();
  }

}
