import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { PhysicianService } from 'src/app/services/physician/physician.service';

@Component({
  selector: 'app-m-unbilled-encounters',
  templateUrl: './m-unbilled-encounters.component.html',
  styleUrls: ['./m-physician.scss']
})
export class MUnbilledEncountersComponent implements OnInit {
  public submitted: boolean = false;
  public FilterForm: FormGroup;
  public listOfFacilities: Array<any> = [];
  public listOfPatients: Array<any> = [];
  public msrlistOfPatients: Array<any> = [];
  public searchKey: string = '';
  public p: number = 1;
  public request: any = {};
  device = false;
  public filterObj: any = {};
  orderBy = 'desc';
  orderByPatient = 'desc';
  orderByAccount = 'desc';
  orderByFacility = 'desc';
  orderByencounterseendate = 'desc';
  orderByadmit_Datetime = 'desc';
  sortColumnBy = 'admission_Date_sort';
  public timeout: any = null;
  public residentAccess: string;

  hideSideNav = false;
  toggleType = 'close';
  sortOrders = ['desc', 'asc'];
  activeTab: string = 'myPatients';

  // Make Math available to template
  Math = Math;

  sortOptions = [
    {
      id: 'account_Number', name: 'Account Number', sortOrders: ['desc', 'asc']
    },
    {
      id: 'patient_Name', name: 'Patient Name', sortOrders: ['desc', 'asc']
    },
    {
      id: 'facility_Name', name: 'Facility', sortOrders: ['desc', 'asc']
    },
    {
      id: 'encounterseendate_sort', name: 'Encounter Seen Date', sortOrders: ['desc', 'asc']
    },
    {
      id: 'admission_Date_sort', name: 'Admission Date', sortOrders: ['desc', 'asc']
    },
  ];

  constructor(private readonly physicianService: PhysicianService, private readonly commonServ: CommonService,
     private readonly appComp: AppComponent, private readonly encrDecr: EncrDecrServiceService,
     private readonly fb: FormBuilder, private readonly router: Router) { 
       this.residentAccess = this.appComp.userAccess.residentAccess;
     }

  ngOnInit(): void {
    this.appComp.loadPageName('Unbilled Encounters', 'physicianTab');
    this.FilterForm = this.fb.group({
      ddlFacility: ['',Validators.required]
    });
    if (history.state.filterObj) {
      this.getFacilities();
      this.filterObj = history.state.filterObj;
      this.FilterForm.get('ddlFacility')?.setValue(this.filterObj.ddlFacility);
      this.searchKey = this.filterObj.searchByName;
      this.p = this.filterObj.p;
      this.getUnBilledEncounters();
    }else{
      this.getFacilitiesAndPatients();
    }

    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (window.innerWidth <= 575 || window.innerWidth >= 575)) {
      // some code..
      this.device = true;
    } else {
      this.device = false;
    }
    
  }

  get f() { return this.FilterForm.controls; }

  getFacilitiesAndPatients() {
    this.submitted = true;
    this.commonServ.getFacilitiesByUserType('PHYSICIAN').subscribe((p: any) => {
      this.listOfFacilities = p;
      if (this.listOfFacilities.length > 0) {
        this.FilterForm.get('ddlFacility')?.setValue(this.listOfFacilities[0].facilityName);
        this.getUnBilledEncounters();
      }
    }, error => { console.error(error.status); });
  }

  getFacilities() {
    this.submitted = true;
    this.commonServ.getFacilitiesByUserType('PHYSICIAN').subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }

  getUnBilledEncounters() {
    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.ParamOne = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.physicianService.getUnBilledEncounters(this.request).subscribe((p: any) => {
      if (p?.length) {
        this.listOfPatients = this.sortOrderBy(p, this.sortColumnBy, this.orderBy);
        this.msrlistOfPatients = [...this.listOfPatients];
      }
      else {
        this.listOfPatients = [];
        this.msrlistOfPatients = [];
      }
      this.request = {};
      this.commonServ.stopLoading();
      this.listOfPatients =this.filterByValue(this.msrlistOfPatients,this.searchKey);
    }, error => {
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  openDeleteConfirmPopup(item) {
    if (confirm('Do you want to contine to Delete this Missing Encounter?')) {
      this.commonServ.startLoading();
      this.request.sID = this.encrDecr.set(item.notesCount);
      this.commonServ.DeleteMissingEncounter(this.request).subscribe((p: any) => {
        this.request = {};
        this.getUnBilledEncounters();
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  }

  sortColumn(columnBy) {
    this.sortColumnBy = columnBy;

    if (columnBy == 'patient_Name') {
      this.orderByPatient = this.orderByPatient == 'asc' ? 'desc' : 'asc';
      this.orderBy = this.orderByPatient;
    }
    else if (columnBy == 'account_Number') {
      this.orderByAccount = this.orderByAccount == 'asc' ? 'desc' : 'asc';
      this.orderBy = this.orderByAccount;
    }
    else if (columnBy == 'facility_Name') {
      this.orderByFacility = this.orderByFacility == 'asc' ? 'desc' : 'asc';
      this.orderBy = this.orderByFacility;
    }
    else if (columnBy == 'encounterseendate_sort') {
      this.orderByencounterseendate = this.orderByencounterseendate == 'asc' ? 'desc' : 'asc';
      this.orderBy = this.orderByencounterseendate;
    }
    else if (columnBy == 'admission_Date_sort') {
      this.orderByadmit_Datetime = this.orderByadmit_Datetime == 'asc' ? 'desc' : 'asc';
      this.orderBy = this.orderByadmit_Datetime;
    }
    this.listOfPatients = this.sortOrderBy(this.listOfPatients, columnBy, this.orderBy)
    this.msrlistOfPatients = [...this.listOfPatients];

  }

  sortOrderBy(list, sortBy, sortOrder) {
    let newArray = [];
    this.p = 1;
    if (sortOrder == 'asc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? -1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? 1 : sortNest;
          return sort;
        }
      );
    }
    else if (sortOrder == 'desc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? 1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? -1 : sortNest;
          return sort;
        });
    }
    return newArray;
  }

  search(searchKey) {

    clearTimeout(this.timeout);

    this.timeout = setTimeout(() => {
      if (this.msrlistOfPatients.length)
        this.listOfPatients = this.filterByValue(this.msrlistOfPatients, searchKey)
    }, 2000);

  }

  filterByValue(array, string) {
    return array.filter(o => {
      return Object.keys(o).some(k => {
        if (typeof o[k] === 'string')
          return o[k].toLowerCase().includes(string.toLowerCase());
      });
    });
  }

  toggleSideNav(type): void {
    this.toggleType = type;
    this.hideSideNav = !this.hideSideNav;
  }
  onChangeForSort(event) {
    const deviceValue = event.value;
    this.sortOrders = this.sortOptions.filter(x => x.id == deviceValue)[0].sortOrders;
    this.sortColumn(deviceValue);
  }
  onChangeForSortOrder(event) {
    const deviceValue = event.value;
    this.orderBy = deviceValue;
    this.sortColumn(this.sortColumnBy);
  }

}
