<div class="max-h-80 w-full bg-white mobile-p-3" [formGroup]="patientForm">
    <div class="w-full m-b-70">
        <table class="table">
            <tbody>
                <tr>
                    <td>
                        <span class="font-medium">First Name <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <input class="form-control input-border" type="text" formControlName="txtFirstName"
                            [(ngModel)]="patient.first_name">
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Last Name <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <input class="form-control input-border disabled" type="text" formControlName="txtLastName"
                            [(ngModel)]="patient.last_name">
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Account #</span>
                    </td>
                    <td>
                        <input class="form-control input-border disabled" type="text" formControlName="txtAccountNo"
                            [(ngModel)]="patient.account_number">
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">MRN <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <input class="form-control input-border disabled" type="text" formControlName="txtMRN"
                            [(ngModel)]="patient.patient_mrn">
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">DOB <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <span class="form-control input-border disabled">{{patient.dob | date:
                            'MM/dd/yyyy'}}</span>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Pt. Class <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <select formControlName="role" class="form-control"
                            [ngClass]="{ 'is-invalid': submitted && f['ddlPatientClass'].errors}"
                            formControlName="ddlPatientClass" [(ngModel)]="patient.patient_class">
                            <option [value]="null">Select Patient Class</option>
                            <option value="I"><span>InPatient</span></option>
                            <option value="O"><span>OutPatient</span></option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Facility <span class="text-danger">*</span></span>
                    </td>
                    <td>
                       <input class="form-control input-border disabled" type="text" formControlName="ddlFacilityName"
                            [(ngModel)]="patient.facility_name">
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Admit <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <ng-container *ngIf="patient.is_admission_date_editable==false; else admElse">
                            <mat-form-field [subscriptSizing]="'dynamic'">
                                <mtx-datetimepicker #datetimePicker5 [type]="type" [mode]="mode"
                                    [multiYearSelector]="multiYearSelector" [startView]="startView"
                                    [twelvehour]="twelvehour" [timeInterval]="timeInterval" [touchUi]="touchUi"
                                    [timeInput]="timeInput">
                                </mtx-datetimepicker>
                                <input [mtxDatetimepicker]="datetimePicker5" formControlName="txtAdmissionDate" matInput
                                    [(ngModel)]="patient.admit_datetime" class="form-control"
                                    [ngClass]="{ 'is-invalid': submitted && f['txtAdmissionDate'].errors}">
                                <mtx-datetimepicker-toggle [for]="datetimePicker5"
                                    matSuffix></mtx-datetimepicker-toggle>
                            </mat-form-field>
                        </ng-container>
                        <ng-template #admElse>
                            <input type="text" formControlName="txtAdmissionDate"
                                class="form-control input-border bg_color_gray" [(ngModel)]="patient.admit_datetime" />
                        </ng-template>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Assign To <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <input class="form-control input-border" type="text" formControlName="txtAssignPatientTo"
                            [(ngModel)]="patient.physician_name">
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Department <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <input class="form-control input-border disabled" type="text" formControlName="txtDepartment"
                            [(ngModel)]="patient.patient_location">
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Room <span class="text-danger">*</span></span>
                    </td>
                    <td>
                       <input class="form-control input-border"
                            [ngClass]="{ 'is-invalid': submitted && f['txtRoomNo'].errors}" type="text"
                            formControlName="txtRoomNo" [(ngModel)]="patient.room_number">
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Bed <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <input class="form-control input-border"
                            [ngClass]="{ 'is-invalid': submitted && f['txtBedNo'].errors}" type="text"
                            formControlName="txtBedNo" [(ngModel)]="patient.bed_number">
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">SSN</span>
                    </td>
                    <td>
                        <input class="form-control input-border" maxlength="9" type="text" formControlName="txtSSN"
                            [(ngModel)]="patient.ssn">
                        <div class="text-danger" *ngIf="submitted && f['txtSSN'].errors?.['maxLength']">Max length 9
                            characters</div>
                        <div class="text-danger" *ngIf="submitted && f['txtSSN'].errors?.['pattern']">SSN should be a
                            number</div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="upload-section upload-footer2">
        <div class="flex flex-col">
            <button type="button" class="upload-btn" (click)="updatePatient(patient)">Submit</button>
        </div>
    </div>
</div>