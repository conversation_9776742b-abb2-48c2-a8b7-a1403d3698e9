import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { ToastrService } from 'ngx-toastr';
import {saveAs} from 'file-saver';
declare let $:any;

@Component({
  selector: 'app-m-upload-attachment',
  templateUrl: './m-upload-attachment.component.html',
  styleUrls: ['./m-physician.scss']
})
export class MUploadAttachmentComponent  {

  @Input() lisfOfAttachments:Array<any>=[];
  @Input() PatientObject:any={};
  public isFileRequired:boolean=true;
  public isCommentLength:boolean=false;
  public isFile:boolean=false;
  public isFileValidName:boolean=false;
  public isFileSize:boolean=false;
  public fileName:string="";
  public comment:string="";
  public request:any={};
  public progress: number;
  public message: string;
  public formData = new FormData();
  public submitted:boolean=false;
  @Input() userType:string='';
  @Output() eventUpdateCount= new EventEmitter<any>();
  public listOfPatients:Array<any>=[];
  public fileMimeArray:Array<string>=["image/jpg","image/png","image/jpeg","image/bat","application/pdf","image/gif","image/tiff","image/heif","image/heic","application/msword",
                      "application /vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain",
                      "application/vnd.ms-excel","text/comma-separated-values","application/octet-stream"];

  constructor(private readonly commonServ:CommonService,private readonly encrDecr: EncrDecrServiceService,private readonly toastr: ToastrService) { }


  uploadChange(files) {
    this.formData= new FormData();
    const file = files[0];
    if (file.name.split('.').length > 2)
      this.isFileValidName = true;
    else
      this.isFileValidName = false;
    if (!this.fileMimeArray.includes(file.type))
      this.isFile = true;
    else
      this.isFile = false;
    if (file.size > 20000000)
      this.isFileSize = true;
    else
      this.isFileSize = false;
   if (files.length > 0)
    {
      this.formData.append("File", files[0]);
      this.fileName=files[0].name;
      this.isFileRequired=false;
    }
    else {
      this.isFileRequired = true;
    }
  }

  uploadSubmit(pObj) {
    this.submitted = true;
    let cmtLenght = this.comment.length;
    if (cmtLenght > 400) {
      this.isCommentLength = true;
    }
    else {
      this.isCommentLength = false;
    }

    if (this.isFileRequired || this.isCommentLength || this.isFileSize || this.isFileValidName || this.isFile) {
      return;
    }

    this.commonServ.startLoading();
    this.isCommentLength = false;
    this.isFileSize = false;
    this.isFileValidName = false;
    this.isFile = false;
    this.formData.append('Account_Number', this.encrDecr.set(pObj.account_Number));
    this.formData.append('Facility_Name', this.encrDecr.set(pObj.facility_Name));
    this.formData.append('Comments', this.encrDecr.set(this.comment));

    this.commonServ.uploadFiles(this.formData).subscribe((p: any) => {
      this.isFileRequired = true;
      this.submitted=false;
      this.formData.delete("File");
      this.formData.delete('Account_Number');
      this.formData.delete('Facility_Name');
      this.formData.delete('Comments');
      this.comment = "";
      $("#attchFileId").val('');
      this.fileName = "";
      if (p.statusCode == 200) {
        this.toastr.success("uploaded successfully");
        this.lisfOfAttachments.push(p.response);
      }
      else {
        this.toastr.error(p.message);
      }
      this.eventUpdateCount.emit(this.PatientObject);
      this.commonServ.stopLoading();
    }, error => {
      this.formData.delete(this.fileName);
      this.formData.delete('Account_Number');
      this.formData.delete('Facility_Name');
      this.formData.delete('Comments');
      this.commonServ.stopLoading();
      console.error(error.status);
    });

  }
  
  downloadAttachment(fileName){
    this.commonServ.startLoading();
    this.commonServ.downloadAttachment(fileName).subscribe((p: any) => {
      saveAs(this.encrDecr.get(p))
      this.commonServ.stopLoading();
    },error => {
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  deleteAttachment(list, item) {
    if (confirm('Do you want to delete this attachment?')) {
      this.commonServ.startLoading();
      this.request.sDOC_ID = this.encrDecr.set(item.doC_ID);
      this.commonServ.deleteAttachment(this.request).subscribe((p: any) => {
        this.request = {};
        if (p > 0) {
          const index = list.indexOf(item);
          if (index > -1) {
            list.splice(index, 1);
          }
        }
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  }

  closeViewDocs(id){
    $("#viewImg-"+id).modal('hide');
  }

}