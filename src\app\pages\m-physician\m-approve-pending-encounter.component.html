<div class="details-header">
    <button class="back-btn" (click)="back()">
        <img src="assets/icons/icon-caret-Left.svg" class="img-icon">
    </button>
    <div class="patient-name ">{{patient.patient_Name}} {{patient.sex}} ({{patient.age}}Y)</div>
</div>
<div class="w-full bg-white">
<div class="details-buttons mx-2">
            <button class="action-btn" [routerLink]="['/m/physician/start-new-encounter']"
                [state]="{patient:patient,encounterSeenDate:encounterSeenDate,backUrl:'/m/physician/pending-approval-encounters',facilityType:patient.isPrimeFacility,filterObj:filterObj}"
                title="Start New Encounter">
                <img src="assets/icons/icon-start.svg" class="img-icon px-6">
            </button>
            <button class="action-btn" (click)="viewPatientHistory()">
                <img src="assets/icons/icon-history.svg" class="img-icon">
            </button>
            <button class="action-btn" (click)="viewNotes()">
                <img src="assets/icons/icon-note.svg" class="img-icon">
            </button>
            <button class="action-btn" (click)="viewAttachments()">
                <img src="assets/icons/icon-attachment.svg" class="img-icon">
            </button>
      </div>
<div class="patient-card mt-2 mx-2" *ngFor="let item of listOfPendingEncounters">
    <div class="w-full mobile-p-4">
        <table class="table table-striped">
            <tbody>
                <tr>
                    <td class="text-secondary2">Account</td>
                    <td><span class="font-medium">{{patient.account_Number || '-'}}</span></td>
                </tr>
                <tr>
                    <td class="text-secondary2">Facility</td>
                    <td><span class="font-medium">{{patient.facility_Name || '-'}}</span></td>
                </tr>
                <tr>
                    <td class="text-secondary2">Physician</td>
                    <td><span class="font-medium">{{item.physicianname || '-'}}</span></td>
                </tr>
                <tr>
                    <td class="text-secondary2">Encounter Seen Date</td>
                    <td><span class="font-medium">{{item.encounterseendateparam | date:
                            'MM/dd/yyyy hh:mm:ss a' }}</span></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="code-content">

        <!-- CPT Starts -->
        <div class="patient-card flex flex-col mobile-p-3">
            <div class="flex justify-between mb-2">
                <span class="font-medium px-2">CPT Codes</span>
            </div>
            <div class="flex flex-col">
                <div class="p-2 border-b" *ngFor="let cpt of item.listofTestCPTS">
                    <div class="flex items-center">
                        <div class="flex flex-col ml-2">
                            <div>{{cpt}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CPT Ends -->
        <!-- ICD Starts -->
        <div class="patient-card flex flex-col mobile-p-3">
            <div class="flex justify-between mb-2">
                <span class="font-medium px-2">ICD Codes</span>

            </div>
            <div class="flex flex-col">
                <div class="p-2 border-b" *ngFor="let icd of item.listofTestICDS">
                    <div class="flex items-center">
                        <div class="flex flex-col ml-2">
                            <div>{{icd}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="details-buttons mt-2">
          

            <button class="action-btn" title="Delete Encounter" (click)='deleteEncounter(item)'>
                <img src="assets/icons/icon-trash-red.svg" class="img-icon px-6">
            </button>
            <button class="action-btn" title="Approve Encounter" *ngIf="residentAccess=='NO'"
                (click)='approveEncounter(item)'>
                <i class="fas fa-check-double text-success"></i>
            </button>
            <button class="action-btn" title="Edit Encounter" [routerLink]="['/m/physician/start-new-encounter']"
                [state]="{patient:patient,encounterSeenDate:item.encounterseendateparam,backUrl:'/m/physician/pending-approval-encounters',facilityType:patient.isPrimeFacility,encounterId: item.encounteR_ID,filterObj:filterObj}">
                <img src="assets/icons/icon-edit.svg" class="action-icon">
            </button>

        </div>
       
        <!-- ICD Ends -->
    </div>

</div>
</div>
  <!-- Patient History Popup -->
    <div *ngIf="showHistoryPopup">
        <div class="modal-backdrop"></div>
        <div class="actions-popup" [@slideUp]>
            <div class="actions-header">
                <span>Past Encounters</span>
                <button class="close-btn" (click)="closePatientHistoryPopup()">
                    <img src="assets/icons/icon-close.svg" class="img-icon">
                </button>
            </div>
            <div class="max-h-70">
                <app-m-view-history [listOfPatientHistory]='listOfPatientHistory' [patient]='patient'
                    [historyTotalCount]="historyTotalCount"></app-m-view-history>
            </div>
        </div>
    </div>

    <!-- send note Popup Starts -->
    <div *ngIf="showNotesPopup">
        <div class="modal-backdrop"></div>
        <div class="actions-popup" [@slideLeft]>
            <div class="actions-header bg-white">
                <span>Notes</span>
                <button class="close-btn" (click)="closeNotesPopup()">
                    <img src="assets/icons/icon-close.svg" class="img-icon">
                </button>
            </div>
            <div class="max-h-80 min-h-80 bg-white">
                <app-m-send-note [PatientObject]='patient' [listOfUsersAndGroups]="listOfUsersAndGroups"
                    [lisfOfSentNotes]="lisfOfSentNotes" [selectedUsersAndGroups]="selectedUsersAndGroups"
                    (eventListOfNotes)="openNotes(patient)"></app-m-send-note>
            </div>
        </div>
    </div>
    <!-- send note Popup Ends -->

    <!-- attachment popup starts -->
    <div *ngIf="showAttachmentsPopup">
        <div class="modal-backdrop"></div>
        <div class="actions-popup" [@slideLeft]>
            <div class="actions-header bg-white">
                <span>Attachments</span>
                <button class="close-btn" (click)="closeAttachmentsPopup()">
                    <img src="assets/icons/icon-close.svg" class="img-icon">
                </button>
            </div>
            <div class="max-h-80 min-h-80 bg-white">
                <app-m-upload-attachment (eventUpdateCount)="updateAttCount(patient)"
                    [lisfOfAttachments]="lisfOfAttachments" [PatientObject]="patient"
                    [userType]="'PHYSICIAN'"></app-m-upload-attachment>
            </div>
        </div>
    </div>
    <!-- attachment popup ends -->