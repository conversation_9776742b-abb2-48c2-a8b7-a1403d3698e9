<div class="patient-list-container">
    <!-- Header Section -->
    <div class="header-section" [formGroup]="FilterForm">
        <!-- Search Bar -->
        <div class="search-container">
            <div class="search-input-wrapper">
                <i class="fas fa-search search-icon" (click)="getPendingApprovalData()"></i>
                <input type="text" class="search-input" placeholder="Search Name, Account, MRN, Room"
                 [(ngModel)]="searchByName" [ngModelOptions]="{standalone: true}" (keyup)="search(searchByName)">
            </div>
            <button class="sort-btn">
                <img src="assets/icons/icon-sort.svg" class="img-icon">
            </button>
            <button class="filter-btn">
                <img src="assets/icons/icon-filter-active.svg" class="img-icon">
            </button>
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <div class="facility-dropdown">
                <mat-select id="ddlFacility" class="form-control" formControlName="ddlFacility" (selectionChange)="getPendingApprovalData()"
                    [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                    <mat-option value="">---Select Facility---</mat-option>
                    <mat-option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                        <span>{{s.facilityName}}</span>
                    </mat-option>
                </mat-select>
            </div>
            <div class="tabs">
                <button class="tab-btn active" [class.active]="activeTab === 'myPatients'"
                    (click)="setActiveTab('myPatients')">
                    My Patients
                </button>
                <button class="tab-btn" [class.active]="activeTab === 'hidden'" (click)="setActiveTab('hidden')">
                    Hidden
                </button>
            </div>
        </div>
    </div>

    <!-- Patient Cards Starts -->
    <app-m-patients [listOfPatients]="listOfPatients" [p]="p" [pageName]="'/m/physician/pending-approval-encounters'"
        [FilterForm]="FilterForm" [totalCount]="0" [searchByName]="searchByName" [device]="device"
        (enventUpdateSortObj)="updateSortObj($event)" [orderBy]="orderBy" [sortColumnBy]="sortColumnBy"
        (eventListOfPatients)="getPendingApprovalData()" (eventChkAllForApprove)="chkAllForApprove($event)"
        (eventChkHaveanyToApprove)="chkHaveanyToApprove()" [userAccess]="userAccess"
        [isSelectAllChecked]="isSelectAllChecked"></app-m-patients>
    <!-- Patient Cards Ends -->

</div>