 <!-- Assign to Others -->
<div class="max-h-80 w-full bg-white mobile-p-3" *ngIf="isAssignToOthers; else resiPop" [formGroup]="FilterPhyForm">
    <div class="w-full m-b-10">
        <mat-select id="ddlPhysician" class="form-control" formControlName="ddlPhysician"
            [ngClass]="{ 'is-invalid': submitted && f1['ddlPhysician'].errors}">
            <mat-option value="">---Select Physician---</mat-option>
            <mat-option [value]="s.physicianEmaiId" *ngFor="let s of listOfPhysicians">
                <span>{{s.physicianName}}</span>
            </mat-option>
        </mat-select>
    </div>
    <div class="upload-section upload-footer2">
        <div class="flex flex-col">
            <button type="button" class="upload-btn" (click)='assignToOthersSave()'>Save</button>
        </div>
    </div>
</div>

 <!-- Assign to Resident -->
<ng-template #resiPop>
<div class="max-h-80 w-full bg-white mobile-p-3" [formGroup]="FilterResiForm">
    <div class="w-full m-b-10">
        <mat-select id="ddlResiPhysician" class="form-control" formControlName="ddlResiPhysician"
            [ngClass]="{ 'is-invalid': submitted1 && f2['ddlResiPhysician'].errors}">
            <mat-option value="">---Select Physician---</mat-option>
            <mat-option [value]="s.residentEmail" *ngFor="let s of listOfProvider">
                <span>{{s.residentName}}</span>
            </mat-option>
        </mat-select>
    </div>
    <div class="upload-section upload-footer2">
        <div class="flex flex-col">
            <button type="button" class="upload-btn" (click)='assignToResidentsSave()'>Save</button>
        </div>
    </div>
</div>
</ng-template>