import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { ToastrService } from 'ngx-toastr';
import { PhysicianService } from 'src/app/services/physician/physician.service';
import { ExcelServices } from 'src/app/services/common/ExcelService';
import { DatePipe } from '@angular/common';
declare let $: any;

@Component({
  selector: 'app-my-patients',
  templateUrl: './my-patients.component.html',
  styles: []
})
export class MyPatientsComponent implements OnInit {
  public submitted: boolean = false;
  public FilterForm: FormGroup;
  public listOfFacilities: Array<any> = [];
  public listOfProvider: Array<any> = [];
  public request: any = {};
  public p: number = 1;
  public listOfPatients: Array<any> = [];
  public timeout: any = null;
  public searchByName: string = '';
  device = false;
  orderBy = 'desc';
  sortColumnBy = 'room_Number';
  public filterObj: any = {};
  hideSideNav = false;
  toggleType = 'close';
  public messageCount: number = 0;
  sortOrders = ['desc', 'asc'];
  sortOptions = [
    {
      id: 'account_Number', name: 'Account Number', sortOrders: ['desc', 'asc']
    },
    {
      id: 'patient_Name', name: 'Patient Name', sortOrders: ['desc', 'asc']
    },
    {
      id: 'facility_Name', name: 'Facility', sortOrders: ['desc', 'asc']
    },
    {
      id: 'age', name: 'Age', sortOrders: ['desc', 'asc']
    },
    {
      id: 'room_Number', name: 'Room', sortOrders: ['desc', 'asc']
    },
    {
      id: 'admission_Type', name: 'Patient Type', sortOrders: ['desc', 'asc']
    },
    {
      id: 'arithmetic_Mean_LOS', name: 'LOS', sortOrders: ['desc', 'asc']
    },
    {
      id: 'reimbursement_Type', name: 'Payer Class', sortOrders: ['desc', 'asc']
    },
    {
      id: 'attending_Physician_InApp', name: 'Attending Provider', sortOrders: ['desc', 'asc']
    }
  ];

  constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService
    , private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly toastr: ToastrService, private readonly physicianService: PhysicianService, private readonly excelService: ExcelServices, public datepipe: DatePipe) { }

  ngOnInit() {
    this.FilterForm = this.fb.group({
      ddlFacility: [''],
      ddlPatients: ['']
    });
    if (history.state.filterObj) {
      this.filterObj = history.state.filterObj;
      this.FilterForm.get('ddlFacility')?.setValue(this.filterObj.ddlFacility);
      this.FilterForm.get('ddlPatients')?.setValue(this.filterObj.ddlPatients);
      this.searchByName = this.filterObj.searchByName;
      this.p = this.filterObj.p;
      this.getFacilities();
      this.getMyPatientsDetails();
    }
    else{
      this.getFacilitiesAndPatient();
    }
    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (window.innerWidth <= 575 || window.innerWidth >= 575)) {
      // some code..
      this.device = true;
    } else {
      this.device = false;
    }
    

  }
  get f() { return this.FilterForm.controls; }

  getFacilitiesAndPatient() {
    this.submitted = true;
    this.commonServ.getFacilitiesByUserType('PHYSICIAN').subscribe((p: any) => {
      this.listOfFacilities = p;
      if (this.listOfFacilities.length > 0) {
        this.FilterForm.get('ddlFacility')?.setValue(this.listOfFacilities[0].facilityName);
        this.getMyPatientsDetails();
      }
    }, error => { console.error(error.status); });
  }
  getFacilities() {
    this.submitted = true;
    this.commonServ.getFacilitiesByUserType('PHYSICIAN').subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }
  updateSortObj(obj: any) {
    this.sortColumnBy = obj.sortColumnBy;
    this.orderBy = obj.orderBy;
  }
  childListOfPatients(page){
    this.p=page;
    this.getMyPatientsDetails();
  }
  getMyPatientsDetails() {
    this.submitted = true;
    this.commonServ.startLoading();
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.IsHide = this.encrDecr.set(this.FilterForm.value.ddlPatients);
    this.request.sPageSize = this.encrDecr.set('14');
    this.request.sPageIndex = this.encrDecr.set(this.p);
    this.request.Searchstring = this.encrDecr.set(this.searchByName);
    this.physicianService.GetMyPatientsDetails(this.request).subscribe((p: any) => {
      if (p?.physicianPatientsResponses.length) {
        this.listOfPatients = this.sortOrderBy(p.physicianPatientsResponses, this.sortColumnBy, this.orderBy);
        this.appComp.loadPageName('My Patients' + '(' + p.physicianPatientsResponses.length + ')', 'physicianTab');
      }
      else {
        this.listOfPatients = [];
        this.appComp.loadPageName('My Patients' + '(' + 0 + ')', 'physicianTab');
      }
      this.messageCount = p.physicianPatientsResponses.length;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); }
    )

  }

  FilterPatientDetails() {
    this.submitted = true;
    this.commonServ.startLoading();
    this.p=1;
    this.getMyPatientsDetails();
    this.commonServ.stopLoading();
  }

  onKeyPatientSearch() {
    if (this.FilterForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.p=1;
    this.getMyPatientsDetails();
    this.commonServ.stopLoading();
  }

  RemovePatient(item) {
    if (confirm('Do you want to remove this patient?')) {
      this.submitted = true;
      this.commonServ.startLoading();
      this.request.Account_Number = this.encrDecr.set(item.account_Number);
      this.request.FacilityName = this.encrDecr.set(item.facility_Name);
      this.physicianService.RemovePatient(this.request).subscribe((p: any) => {
        this.request = {};
        this.getMyPatientsDetails();
        this.commonServ.stopLoading();
        this.toastr.success("Removed Patient successfully", '', { timeOut: 2500 });
      }, error => { console.error(error.status); }
      )
    }
  }

  exportAsXLSX(): void {
    let fileName = this.appComp.userName + "_MyPatients_" + (this.datepipe.transform(new Date(), 'yyyy-MM-dd'));
    this.commonServ.startLoading();
    this.physicianService.DownLoadPateintList(this.FilterForm.value.ddlPatients).subscribe((p: any) => {
      if (p) {
        this.excelService.exportAsExcelFile(p, fileName);
      }
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  sortOrderBy(list, sortBy, sortOrder) {
    let newArray = [];
    if (sortOrder == 'asc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? -1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? 1 : sortNest;
          return sort;
        }
      );
    }
    else if (sortOrder == 'desc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? 1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? -1 : sortNest;
          return sort;
        });
    }
    return newArray;
  }

  toggleSideNav(type): void {
    this.toggleType = type;
    this.hideSideNav = !this.hideSideNav;
  }
  onChangeForSort(deviceValue) {
    this.sortOrders = this.sortOptions.filter(x => x.id == deviceValue)[0].sortOrders;
    this.sortColumnBy=deviceValue;
    this.updateSortObj({ sortColumnBy: this.sortColumnBy, orderBy: this.orderBy });
    this.getMyPatientsDetails();
  }
  onChangeForSortOrder(deviceValue) {
    this.orderBy = deviceValue;
    this.updateSortObj({ sortColumnBy: this.sortColumnBy, orderBy: this.orderBy });
    this.getMyPatientsDetails();
  }
}
