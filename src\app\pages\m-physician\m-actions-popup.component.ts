import { Component, Input } from '@angular/core';
import { MPatientsComponent } from './m-patients.component';

@Component({
  selector: 'app-m-actions-popup',
  templateUrl: './m-actions-popup.component.html',
  styleUrls: ['./m-physician.scss']
})
export class MActionsPopupComponent {
  @Input() patient: any = {};
  constructor(private readonly patientCmp: MPatientsComponent) { }

  viewPatientHistory(item: any) {
    this.patientCmp.viewPatientHistory(item);
  }

  viewAttachments(item: any) {
    this.patientCmp.viewAttachments(item);
  }

  viewNotes(item: any) {
    this.patientCmp.viewNotes(item);
  }

  viewEditPatient(item: any) {
    this.patientCmp.viewEditPatient(item);
  }

  viewAssign(item: any, type: string) {
    this.patientCmp.viewAssign(item, type);
  }

  openUpdatePostToBillerPop(pObj) {
    this.patientCmp.openConfirmModel(pObj,'want to post to biller');
  }

  openCloseHospitalizationPop(pObj) {
    this.patientCmp.openConfirmModel(pObj,'close hospitalization');
  }

}
