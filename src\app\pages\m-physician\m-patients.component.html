<div class="patient-cards">
    <div class="patient-card p-3 flex flex-col"
        *ngFor="let item of listOfPatients | paginate: { itemsPerPage: 14, currentPage: p,totalItems:totalCount}; let i = index;">
        <div class="flex flex-col p-6">
            <div class="flex">
                <span class="font-medium text-break"
                    [ngClass]="{'hasTodayEncounter':(item.hasPreviousEncounter=='1' && item.resetPriorEncounterStatus=='1'),'hasDraftEncounter':(item.hasPreviousEncounter=='1' && item.encounteR_ID!='0' && item.encounteR_ID!=null),'hasPriorEncounter':(item.hasPreviousEncounter=='1' && item.encounteR_ID==null && item.resetPriorEncounterStatus==null),'hasNoEncounter':(item.hasPreviousEncounter=='0')}">
                    {{item.patient_Name}}</span>
                <div class="mx-2 h-6 border-l-2"></div>
                <span class="font-normal">{{item.dob}} ({{item.age}}{{item.sex}})</span>
                <div class="mx-2 h-6 border-l-2"></div>
                <span class="font-normal">{{item.facility_Name}}</span>
            </div>
            <div class="my-2 h-1 w-12 border-t-2"></div>
            <div class="grid w-full grid-cols-2 gap-x-4">
                <div>{{item.account_Number || '-'}}</div>
                <div>{{item.room_Number || '-'}}</div>
                <div>{{item.admission_Type || '-'}}</div>
                <div class="flex items-center">
                    <span [style.border-color]="item.color">{{item.arithmetic_Mean_LOS || '-'}}</span>
                    <!-- <div class="mx-2 h-4 border-l-2"></div>
                        <span>GLOS:4</span> -->
                </div>
            </div>
            <div class="mt-2 flex items-center">
                <div><img src="assets/icons/icon-price-transparency.svg" class="img-icon"></div>
                <div class="ml-2">{{item.reimbursement_Type || '-'}}</div>
            </div>
            <div class="mt-2 flex items-center">
                <div><img src="assets/icons/icon-hc-Stethoscope-Doctor.svg" class="img-icon"></div>
                <div class="ml-2">{{item.attending_Physician_InApp || '-'}}</div>
            </div>
        </div>
        <div class="action-buttons">
            <button class="action-btn"
                *ngIf="pageName=='/m/physician/my-group-patients' || pageName=='/m/physician/my-patients'"
                title="Remove patient" (click)='openConfirmModel(item,"remove this patient")'>
                <img src="assets/icons/icon-trash-red.svg" class="img-icon px-6">
            </button>
            <button class="action-btn" *ngIf="item.isHide == 0 && pageName=='/m/physician/my-patients'" title="Hide"
                (click)="openConfirmModel(item,'hide this patient')">
                <img src="assets/icons/icon-hide.svg" class="img-icon px-6">
            </button>
            <button class="action-btn" *ngIf="item.isHide == 1 && pageName=='/m/physician/my-patients'" title="Un-Hide"
                (click)="openConfirmModel(item,'unhide this patient')">
                <img src="assets/icons/icon-eye.svg" class="img-icon px-6">
            </button>
            <button class="action-btn"
                *ngIf="userAccess.physicianModuleAccess=='YES' && pageName=='/m/physician/pending-approval-encounters'"
                title="Approve Encounter" (click)='openConfirmModel(item,"approve this encounters")'>
                <img src="assets/icons/icon-24-white.svg" class="img-icon px-6">
            </button>
            <button class="action-btn" *ngIf="pageName=='/m/physician/discharge-patients'"
                (click)="viewUndoDischarge(item)" title="Un-Discharge">
                <img src="assets/icons/icon-discharge.svg" class="img-icon px-6">
            </button>
            <button class="action-btn" [matMenuTriggerFor]="summaryMenu">
                <img src="assets/icons/icon-change.svg" class="img-icon px-6">
            </button>
            <!-- Play Starts -->
            <button class="action-btn" *ngIf="residentAccess=='YES';else resEls"
                [routerLink]="['/m/physician/approve-pending-encounter']" [state]="{patient:item,backUrl:pageName}"
                title="View/Start New Encounter">
                <img src="assets/icons/icon-start.svg" class="img-icon px-6">
            </button>
            <ng-template #resEls>
                <button class="action-btn" *ngIf="pageName=='/m/physician/pending-approval-encounters';else penAppp"
                    [routerLink]="['/m/physician/approve-pending-encounter']"
                    [state]="{patient:item,encounterSeenDate:encounterSeenDate,backUrl:pageName,facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchByName,ddlFacility:FilterForm.value.ddlFacility,ddlGroups:FilterForm.value.ddlGroups,ddlphybygroup:FilterForm.value.ddlphybygroup,ddlPatients:FilterForm.value.ddlPatients,ddlDepartment:FilterForm.value.ddlDepartment,txtFromDate:FilterForm.value.txtFromDate,txtToDate:FilterForm.value.txtToDate}}"
                    title="View/Start New Encounter">
                    <img src="assets/icons/icon-start.svg" class="img-icon px-6">
                </button>
                <ng-template #penAppp>
                    <button class="action-btn" [routerLink]="['/m/physician/start-new-encounter']"
                        [state]="{patient:item,encounterSeenDate:encounterSeenDate,backUrl:pageName,facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchByName,ddlFacility:FilterForm.value.ddlFacility,ddlGroups:FilterForm.value.ddlGroups,ddlphybygroup:FilterForm.value.ddlphybygroup,ddlPatients:FilterForm.value.ddlPatients,ddlDepartment:FilterForm.value.ddlDepartment,txtFromDate:FilterForm.value.txtFromDate,txtToDate:FilterForm.value.txtToDate}}"
                        title="Start New Encounter">
                        <img src="assets/icons/icon-start.svg" class="img-icon px-6">
                    </button>
                </ng-template>
            </ng-template>
            <!-- Play Ends -->
            <button class="action-btn menu-btn" (click)="openActionsPopup(item)">
                <img src="assets/icons/icon-more.svg" class="img-icon">
            </button>
        </div>

        <!-- Mat Actions Starts -->
        <mat-menu #summaryMenu="matMenu">
            <button mat-menu-item
                *ngIf="(pageName=='/m/physician/my-group-patients' || pageName=='/m/physician/hospital-census') && !item.attending_Physician_InApp.includes(userName)"
                title="Assign to my list" (click)='openConfirmModel(item,"assign this patient to your list")'>
                <img src="assets/icons/icon-note.svg" class="img-icon">
                Assign to My List
            </button>
            <button mat-menu-item title="Assign to Others" (click)="viewAssign(item,'Others')">
                <img src="assets/icons/icon-change.svg" class="img-icon">
                Assign to Others
            </button>
            <button mat-menu-item *ngIf='item.isResidentAcc==1 || item.isResidents==1' title="Assign to Resident"
                (click)="viewAssign(item,'Resident')">
                <img src="assets/icons/icon-24-white.svg" class="img-icon">
                Assign to Resident
            </button>
        </mat-menu>
        <!-- Mat Actions Starts -->

    </div>

    <!-- No Data Message -->
    <div class="patient-card p-3 flex flex-col" *ngIf="listOfPatients.length == 0;else hasData">
        <div class="flex flex-col p-6 items-center justify-center h-20">
            No data
        </div>
    </div>
    <!-- Pagination -->
    <ng-template #hasData>
        <div class="pagination-container">
            <pagination-controls previousLabel="" nextLabel="" (pageChange)="getPatients($event)">
            </pagination-controls>
        </div>
    </ng-template>

</div>


<!-- Actions Popup Starts -->
<div *ngIf="showActionsPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideUp]>
        <div class="actions-header">
            <div class="flex flex-col">
                <span>Actions</span>
                <span class="actions-subtitle text-secondary2">Select actions to perform on
                    <span class="font-medium">{{patient.patient_Name}}</span></span>
            </div>
            <button class="close-btn" (click)="closeActionsPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-70 mobile-p-4">
            <app-m-actions-popup [patient]="patient"></app-m-actions-popup>
        </div>
    </div>
</div>
<!-- Actions Popup Ends -->

<!-- Patient History Popup -->
<div *ngIf="showHistoryPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideUp]>
        <div class="actions-header">
            <span>Past Encounters</span>
            <button class="close-btn" (click)="closePatientHistoryPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80">
            <app-m-view-history [listOfPatientHistory]='listOfPatientHistory' [patient]='patient'
                [historyTotalCount]="historyTotalCount"></app-m-view-history>
        </div>
    </div>
</div>

<!-- send note Popup Starts -->
<div *ngIf="showNotesPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Notes</span>
            <button class="close-btn" (click)="closeNotesPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 bg-white">
            <div class="max-h-56">
                <app-m-send-note [PatientObject]='patient' [listOfUsersAndGroups]="listOfUsersAndGroups"
                    [lisfOfSentNotes]="lisfOfSentNotes" [selectedUsersAndGroups]="selectedUsersAndGroups"
                    (eventListOfNotes)="openNotes(patient)"></app-m-send-note>
            </div>
        </div>
    </div>
</div>
<!-- send note Popup Ends -->

<!-- attachment popup starts -->
<div *ngIf="showAttachmentsPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Attachments</span>
            <button class="close-btn" (click)="closeAttachmentsPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 bg-white">
            <div class="max-h-56">
                <app-m-upload-attachment (eventUpdateCount)="updateAttCount(patient)"
                    [lisfOfAttachments]="lisfOfAttachments" [PatientObject]="patient"
                    [userType]="'PHYSICIAN'"></app-m-upload-attachment>
            </div>
        </div>
    </div>
</div>
<!-- attachment popup ends -->

<!-- Assign Popup Starts -->
<div *ngIf="showAssignPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Physicians</span>
            <button class="close-btn" (click)="closeAssignPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 mobile-p-3 bg-white">
            <app-m-assign-to-others-or-residents [patient]="patient" [listOfPhysicians]="listOfPhysicians"
                [listOfProvider]="listOfProvider" (eventListOfPatients)="getPatients(p)"
                [isAssignToOthers]="isAssignToOthers">
            </app-m-assign-to-others-or-residents>
        </div>
    </div>
</div>
<!-- Assign Popup Ends -->

<!-- Edit Patient Popup Starts -->
<div *ngIf="showEditPatientPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Edit Patient</span>
            <button class="close-btn" (click)="closeEditPatientPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="">
            <app-m-edit-patient [patient]="patient" (eventListOfPatients)="getPatients(p)"
                [userType]="'PHYSICIAN'"></app-m-edit-patient>
        </div>
    </div>
</div>
<!-- Edit Patient Popup Ends -->

<!-- Undo Dischare Confiramation Popup Model Starts -->
<div *ngIf="showUndoDischargePopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Undo Discharge Patient</span>
            <button class="close-btn" (click)="closeUndoDischargePopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 mobile-p-3 bg-white">
            <app-m-undo-discharge-patient [patient]="patient" (eventListOfPatients)="getPatients(p)"
                (eventCloseUndoDischargePopup)="closeUndoDischargePopup()"></app-m-undo-discharge-patient>
        </div>
    </div>
</div>
<!-- Undo Dischare Confiramation Popup Model Ends -->

<!-- Confirm Message Start -->
<div class="modal fade" id="confrimModel" tabindex="-1" aria-labelledby="confrimModelLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header py-2">
                <span class="modal-title" id="confrimModelLabel">Confirm Message</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <div>Do you want to {{confrimMessage}}?</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary btn-sm" data-dismiss="modal">
                    <i class="fa fa-times-circle modal-footer-icon mr-1"></i>
                    No</button>
                <button type="button" class="btn btn-outline-info btn-sm"
                    (click)="confirm(patient)">
                    <i class="fa fa-check-circle modal-footer-icon mr-1"></i>
                    Yes</button>
            </div>
        </div>
    </div>
</div>
<!-- Confirm Message Ends -->