import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { MessageHubService } from 'src/app/services/message-hub/message-hub.service';
declare let $: any;

@Component({
  selector: 'app-m-send-note',
  templateUrl: './m-send-note.component.html',
  styleUrls: ['./m-physician.scss']
})
export class MSendNoteComponent implements OnInit {
  @Input() listOfAttendingProviders: Array<any> = [];
  @Input() selectedListOfAttendingProviders: Array<any> = [];
  @Input() listOfUsersAndGroups: Array<any> = [];
  @Input() selectedUsersAndGroups: Array<any> = [];
  @Input() lisfOfSentNotes: Array<any> = [];
  @Input() savedCoordinatorNotes: string = '';
  @Input() PatientObject: any = {};

  public mDdlAttendingProviderSettings: any = {};
  public mDdlGroupSettings: any = {};
  public mDdlUsersAndGroupsSettings: any = {};
  public noteForm: FormGroup;
  public submitted: boolean = false;
  public request: any = {};
  public request2: any = {};
  public ddlVali: boolean = false;
  public submittedCoordinatorNotes: boolean = false;
  @Output() eventListOfNotes = new EventEmitter<Array<any>>();
  public listOfPatients: Array<any> = [];

  constructor(private readonly commonServ: CommonService, private readonly fb: FormBuilder, private readonly encrDecr: EncrDecrServiceService, private readonly mgsServ: MessageHubService
    , private readonly toastr: ToastrService) { }

  ngOnInit() {
    this.mDdlUsersAndGroupsSettings = {
      singleSelection: false,
      idField: 'itemId',
      textField: 'name',
      enableCheckAll: false,
      itemsShowLimit: 20,
      allowSearchFilter: true
    };
    this.noteForm = this.fb.group({
      txtMessage: ['', [Validators.required, Validators.maxLength(400)]]
    });
  }
  // convenience getter for easy access to form fields
  get f() { return this.noteForm.controls; }

  insertNote(pObj) {
    this.submitted = true;
    let vParUsers = "";
    let vGrpsIds = "";

    if (this.noteForm.invalid) {
      return;
    }

    if (this.selectedUsersAndGroups.length > 0) {
      this.ddlVali = false;
      this.selectedUsersAndGroups.forEach(el => {
        let type = this.listOfUsersAndGroups.find(x => x.itemId == el.itemId);
        if (type.type == "GROUP") {
          vGrpsIds = vGrpsIds + "," + el.itemId;
        }
        else {
          vParUsers = vParUsers + "," + el.itemId;
        }
      });
      let vMesg = "Reg:" + pObj.patient_name + '\n' + " " + "Ac No:" + pObj.account_Number + '\n' + " " + pObj.facility_Name + '\n' + "----------" + this.noteForm.value.txtMessage + '\n' + "----------";
      this.commonServ.startLoading();

      this.request.ParticipantIds = this.encrDecr.set(vParUsers);
      this.request.ParticipantGroupIds = this.encrDecr.set(vGrpsIds);
      this.request.MessageText = this.encrDecr.set(vMesg);
      this.request.sMessageType = this.encrDecr.set('1');
      this.request.MediaType = this.encrDecr.set('TEXT');

      this.request2.ACCOUNT_NUMBER = this.encrDecr.set(pObj.account_Number);
      this.request2.FACILITYNAME = this.encrDecr.set(pObj.facility_Name);
      this.request2.MESSAGE = this.encrDecr.set(this.noteForm.value.txtMessage);
      this.request2.MESSAGE_TYPE = this.encrDecr.set('TEXT');

      this.commonServ.insertNotes(this.request2).subscribe((p: any) => {
        this.request2 = {};
        this.noteForm.reset();
        this.submitted = false;
        this.selectedUsersAndGroups = [];
        this.commonServ.stopLoading();
        this.toastr.success('Notes Saved Successfully!', '', { timeOut: 2500 });
        this.eventListOfNotes.emit(this.PatientObject);
        this.sendMessage(this.request);

      }, error => {
        this.commonServ.stopLoading();
      });
    }
    else {
      this.ddlVali = true;
    }

  }

  sendMessage(request) {
    this.mgsServ.sendMessage(request).subscribe((p: any) => {
      if (p.statusCode == 100) {
        this.toastr.error(p.message, '', { timeOut: 2500 });
      }
      this.request = {};
    },
      error => {
        this.request = {};
        console.error(error.status);
      });
  }

  onItemNoteSelect(item: any) {
    this.ddlVali = false;
  }

}
