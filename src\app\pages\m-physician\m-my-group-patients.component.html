<div class="patient-list-container" id="my-group-patient-list-container">
    <!-- Header Section -->
    <div class="header-section" [formGroup]="FilterForm">
        <!-- Search Bar -->
        <div class="search-container">
            <div class="search-input-wrapper">
                <i class="fas fa-search search-icon" (click)="onKeyPatientSearch()"></i>
                <input type="text" class="search-input" placeholder="Search Name, Account, MRN, Room"
                    [(ngModel)]="searchByName" [ngModelOptions]="{standalone: true}">
            </div>
            <button class="sort-btn" (click)="toggleSideNav('sort')">
                <img src="assets/icons/icon-sort.svg" class="img-icon">
            </button>
            <button class="filter-btn">
                <img src="assets/icons/icon-filter-active.svg" class="img-icon">
            </button>
        </div>

        <!-- Sort Tabs -->
        <div class="filter-tabs mb-3" *ngIf="hideSideNav">
            <div class="facility-dropdown">
                <mat-label>Sort By</mat-label>
                <mat-select id="sortColumn" (selectionChange)="onChangeForSort($event)" class="form-control"
                    [value]="sortColumnBy">
                    <mat-option *ngFor="let sortOption of sortOptions"
                        [value]="sortOption.id">{{sortOption.name}}</mat-option>
                </mat-select>
            </div>
            <div class="facility-dropdown">
                <mat-label>Sort Order</mat-label>
                <mat-select id="sortOrder" (selectionChange)="onChangeForSortOrder($event)" class="form-control"
                    [value]="orderBy">
                    <mat-option *ngFor="let sortOrder of sortOrders" [value]="sortOrder">{{sortOrder}}</mat-option>
                </mat-select>
            </div>
        </div>
        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <div class="facility-dropdown">
                <mat-select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                    (selectionChange)="ddlFacilityChange()" [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                    <mat-option value="">---Select Facility---</mat-option>
                    <mat-option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                        <span>{{s.facilityName}}</span>
                    </mat-option>
                </mat-select>
            </div>
            <div class="facility-dropdown">
                <mat-select id="ddlGroups" class="form-control" formControlName="ddlGroups" (selectionChange)="getphyciansBygroup()"
                    [ngClass]="{ 'is-invalid': submitted && f['ddlGroups'].errors}">
                    <mat-option value="">---Select Group---</mat-option>
                    <mat-option [value]="s.group_name" *ngFor="let s of listOfGroups">
                        <span>{{s.group_name}}</span>
                    </mat-option>
                </mat-select>
            </div>
        </div>
        <div class="filter-tabs mt-2">
            <div class="facility-dropdown">
                <ng-multiselect-dropdown class="" (onSelect)="FilterPatientDetails()"
                    (onDeSelect)="FilterPatientDetails()" (onSelectAll)="getphyciansBygroupPhyAll()"
                    (onDeSelectAll)="getphyciansBygroupPhyAll()" [placeholder]="'---Select Physician---'"
                    formControlName="ddlphybygroup" [data]="listOfPhysiciansandcount" [settings]="mDdlPhysicianSettings"
                    [ngModel]="selectedListOfPhysiciansandcount">
                </ng-multiselect-dropdown>
            </div>
        </div>
    </div>

    <!-- Patient Cards Starts -->
    <app-m-patients [listOfPatients]="listOfPatients" [p]="p" [pageName]="'/m/physician/my-group-patients'"
        [FilterForm]="FilterForm" [totalCount]="totalCount" [searchByName]="searchByName" [device]="device"
        (enventUpdateSortObj)="updateSortObj($event)" [orderBy]="orderBy" [sortColumnBy]="sortColumnBy"
        (eventListOfPatients)="getMyGroupPatientsDetails($event)"
        (eventRemovePatient)="RemoveGroupForPatient($event)"></app-m-patients>
    <!-- Patient Cards Ends -->

</div>