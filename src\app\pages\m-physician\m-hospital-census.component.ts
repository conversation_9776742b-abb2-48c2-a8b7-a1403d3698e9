import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { PhysicianService } from 'src/app/services/physician/physician.service';
import { ExcelServices } from 'src/app/services/common/ExcelService';
import { DatePipe } from '@angular/common';
import { forkJoin } from 'rxjs';
declare let $: any;

@Component({
  selector: 'app-m-hospital-census',
  templateUrl: './m-hospital-census.component.html',
  styleUrls: ['./m-physician.scss']
})
export class MHospitalCensusComponent implements OnInit {
  public submitted: boolean = false;
  public FilterForm: FormGroup;
  public listOfFacilities: Array<any> = [];
  public request: any = {};
  public grprequest: any = {};
  public p: number = 1;
  public listOfPatients: Array<any> = [];
  public timeout: any = null;
  public userName: string;
  public listofDepartments: Array<any> = [];
  public totalCount: number = 0;
  device = false;
  public filterObj: any = {};
  orderBy = 'desc';
  sortColumnBy = 'room_Number';
  constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService
    , private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly physicianService: PhysicianService, private readonly excelService: ExcelServices
    , public datepipe: DatePipe) { }

  ngOnInit() {
    this.appComp.loadPageName('Hospital Census', 'physicianTab');
    this.userName = this.appComp.userAccess.firstName + ' ' + this.appComp.userAccess.lastName;
    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (window.innerWidth <= 575 || window.innerWidth >= 575)) {
      // some code..
      this.device = true;
    } else {
      this.device = false;
    }
    this.FilterForm = this.fb.group({
      ddlFacility: ['All'],
      ddlDepartment: ['All'],
      txtSearchKey: ['']
    });
    if (history.state.filterObj) {
      this.filterObj = history.state.filterObj;
      this.FilterForm.get('ddlFacility')?.setValue(this.filterObj.ddlFacility);
      this.FilterForm.get('ddlDepartment')?.setValue(this.filterObj.ddlDepartment);
      this.FilterForm.get('txtSearchKey')?.setValue(this.filterObj.searchByName);
      this.p = this.filterObj.p;
      this.getFacilitiesForStateBackFun();
    }
    else {
      this.getFacilities();
    }
  }
  get f() { return this.FilterForm.controls; }

  getFacilities() {
    this.commonServ.getFacilitiesByUserType('PHYSICIAN').subscribe((p: any) => {
      this.listOfFacilities = p;
        this.FilterForm.get('ddlFacility')?.setValue(this.listOfFacilities[0].facilityName);
        this.facilityChangeEvent(1);
    }, error => { console.error(error.status); });
  }

  getFacilitiesForStateBackFun() {
    this.commonServ.getFacilitiesByUserType('PHYSICIAN').subscribe((p: any) => {
      this.listOfFacilities = p;
      this.facilityChangeEvent(this.p);
    }, error => { console.error(error.status); });
  }
  updateSortObj(obj: any) {
    this.sortColumnBy = obj.sortColumnBy;
    this.orderBy = obj.orderBy;
  }
  getHospitalCensusData(pno) {

    this.p = pno;
    this.submitted = true;
    this.commonServ.startLoading();
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.MyGroupPatient = this.encrDecr.set(this.FilterForm.value.ddlDepartment);
    this.request.sPageSize = this.encrDecr.set('14');
    this.request.sPageIndex = this.encrDecr.set(pno);
    this.request.Searchstring = this.encrDecr.set(this.FilterForm.value.txtSearchKey);
    this.request.SortColumn = this.encrDecr.set(this.sortColumnBy);
    this.request.SortExpression = this.encrDecr.set(this.orderBy);
    this.physicianService.getHospitalCensusData(this.request).subscribe((p: any) => {
      this.listOfPatients = p.physicianPatientsResponses;
      this.totalCount = p.myPatientCount;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); }
    )
  }

  onFacilityChangeEvent() {
    this.facilityChangeEvent(1);
  }

  facilityChangeEvent(p) {
    this.submitted = true;
    this.commonServ.startLoading();
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.MyGroupPatient = this.encrDecr.set(this.FilterForm.value.ddlDepartment);
    this.request.sPageSize = this.encrDecr.set('14');
    this.request.sPageIndex = this.encrDecr.set(p);
    this.request.Searchstring = this.encrDecr.set(this.FilterForm.value.txtSearchKey);
    this.request.SortColumn = this.encrDecr.set(this.sortColumnBy);
    this.request.SortExpression = this.encrDecr.set(this.orderBy);
    this.grprequest.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);

    forkJoin(
      this.physicianService.getHospitalCensusData(this.request),
      this.commonServ.getDepartmentsByFacility(this.grprequest)
    ).subscribe((p: any) => {
      this.listOfPatients = p[0].physicianPatientsResponses;
      this.totalCount = p[0].myPatientCount;
      this.listofDepartments = p[1];
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }
  
  onKeyPatientSearch() {
    clearTimeout(this.timeout);
    if (this.FilterForm.invalid) {
      return;
    }

    this.timeout = setTimeout(() => {
      this.getHospitalCensusData(1);
    }, 2000);
  }

  exportAsXLSX(): void {
    let fileName = this.appComp.userName + "_MyPatients_" + (this.datepipe.transform(new Date(), 'yyyy-MM-dd'));
    this.commonServ.startLoading();
    this.physicianService.DownLoadPateintList('0').subscribe((p: any) => {
      if (p) {
        this.excelService.exportAsExcelFile(p, fileName);
      }
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }


}
