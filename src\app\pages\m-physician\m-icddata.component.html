<div class="w-full">
    <mat-tab-group (selectedTabChange)="onMatGroupTabClick($event)">
        <mat-tab label="Favorites">
            <ng-template matTabContent>
                <div class="p-3 mb-8">
                    <div class="search-container">
                        <div class="search-input-wrapper">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="Search by keyword"
                             [(ngModel)]="searchICDs" [ngModelOptions]="{standalone: true}">
                        </div>
                    </div>
                    <div class="flex flex-col">
                        <div class="flex" *ngFor="let item of lisfOfICDData |gridFilter:{icdname:searchICDs}:false">
                            <ng-container *ngIf="item.status">
                            <mat-checkbox [color]="'primary'" [checked]="item.isExist" (change)="chkChangeEvent($event)"
                             [id]="item.icD_ID" value="{{item.icdname}}">
                                <span>{{item.icdname}}</span>
                            </mat-checkbox>
                            <div class="flex items-center ml-2">
                                <img src="assets/icons/icon-star-favorite.svg" class="img-icon" (click)="favUnfavICDCodesAdd(false,item)">
                            </div>
                            </ng-container>
                        </div>
                    </div>
                </div>
                <div class="upload-section upload-footer">
                    <button class="add-btn" (click)="addICDData(lisfOfICDData)">Add</button>
                </div>
            </ng-template>
        </mat-tab>
        <mat-tab label="All">
            <ng-template matTabContent>
                <div class="p-3 mb-8">
                    <div class="search-container">
                        <div class="search-input-wrapper">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="Search by keyword"
                             [(ngModel)]="filterICDs" [ngModelOptions]="{standalone: true}" (keyup)="searchICDData()">
                        </div>
                    </div>
                    <div class="flex flex-col">
                        <div class="flex" *ngFor="let item of lisfOfSearchICDsData">
                            <mat-checkbox [color]="'primary'" [checked]="item.isExist"
                            id="sch-{{item.icD_ID}}" value="{{item.icdname}}" (change)="chkChangeEventSearch($event)">
                                <span>{{item.icdname}}</span>
                            </mat-checkbox>
                            <div class="flex items-center ml-2">
                                <img *ngIf="item.status" src="assets/icons/icon-star-favorite.svg" class="img-icon" (click)="favUnfavICDCodesAdd(false,item)">
                                <img *ngIf="!item.status" src="assets/icons/icon-star-default.svg" class="img-icon" (click)="favUnfavICDCodesAdd(true,item)">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="upload-section upload-footer">
                    <button class="add-btn" (click)="addICDDataSearch(lisfOfSearchICDsData)">Add</button>
                </div>
            </ng-template>
        </mat-tab>
    </mat-tab-group>
</div>