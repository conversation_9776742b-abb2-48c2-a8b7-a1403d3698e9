<div class="patient-list-container">
    <!-- Header Section -->
    <div class="header-section" [formGroup]="FilterForm">
        <!-- Search Bar -->
        <div class="search-container">
            <div class="search-input-wrapper">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Search Name, Account, MRN, Room"
                    formControlName="txtSearchKey" (keyup)="onKeyPatientSearch()">
            </div>
            <!-- <button class="sort-btn">
                <img src="assets/icons/icon-sort.svg" class="img-icon">
            </button> -->
            <button class="filter-btn">
                <img src="assets/icons/icon-filter-active.svg" class="img-icon">
            </button>
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <div class="facility-dropdown">
                <mat-select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                    (selectionChange)="onFacilityChangeEvent()" [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                    <mat-option value="">---Select Facility---</mat-option>
                    <mat-option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                        <span>{{s.facilityName}}</span>
                    </mat-option>
                </mat-select>
            </div>
            <div class="facility-dropdown">
                <mat-select id="ddlDepartment" class="form-control" formControlName="ddlDepartment"
                    (selectionChange)="getHospitalCensusData(1)"
                    [ngClass]="{ 'is-invalid': submitted && f['ddlDepartment'].errors}">
                    <mat-option value="All">All Departments</mat-option>
                    <mat-option [value]="s.patienT_LOCATION" *ngFor="let s of listofDepartments">
                        {{s.patienT_LOCATION}}
                    </mat-option>
                </mat-select>
            </div>
        </div>
    </div>

    <!-- Patient Cards Starts -->
    <app-m-patients [listOfPatients]="listOfPatients" [p]="p" [pageName]="'/m/physician/hospital-census'"
        [FilterForm]="FilterForm" [totalCount]="totalCount" [searchByName]="''" [device]="device"
        (enventUpdateSortObj)="updateSortObj($event)" [orderBy]="orderBy" [sortColumnBy]="sortColumnBy"
        (eventListOfPatients)="getHospitalCensusData($event)"></app-m-patients>
    <!-- Patient Cards Ends -->

</div>